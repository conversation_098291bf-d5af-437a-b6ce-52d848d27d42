import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Linking,
  Dimensions,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import NavigationHeader from '../../../CommonComponents/NavigationHeader';
import IndexStyle from '../../../Theme/IndexStyle';
import { IMAGE_CONSTANT } from '../../../Utils/Constants';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { WebUrl, ExamBaseUrl, imgBaseUrl } from '../../../config/apiUrl';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-simple-toast';
import ShadowStyle from '../../../CommonComponents/ShadowStyle';
import { getPreviousDayLeaderboard, TopPerformer } from '../../../services/leaderboardService';

const { width } = Dimensions.get('window');

export default function DailyQuiz() {
  const { isDarkMode } = IndexStyle();
  const navigation = useNavigation<any>();
  const [topPerformers, setTopPerformers] = useState<TopPerformer[]>([]);
  const [loadingPerformers, setLoadingPerformers] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const isFocused = useIsFocused();

  useEffect(() => {
    const fetchTopPerformers = async () => {
      try {
        setLoadingPerformers(true);
        setErrorMessage(null);
        const response = await getPreviousDayLeaderboard();
        if (response.success) {
          setTopPerformers(response.data);
        } else {
          setTopPerformers(response.data);
          setErrorMessage(response.error || 'Failed to load leaderboard');
          Alert.alert('API Error', `Error: ${response.error}. Would you like to retry?`, [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Retry', onPress: () => fetchTopPerformers() },
          ]);
        }
      } catch (error) {
        console.error('Error fetching top performers:', error);
        setErrorMessage('Unexpected error occurred');
        Alert.alert('Error', 'Unexpected error occurred. Would you like to retry?', [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => fetchTopPerformers() },
        ]);
      } finally {
        setLoadingPerformers(false);
      }
    };

    if (isFocused) {
      fetchTopPerformers();
    }
  }, [isFocused]);

  const rewardTiers = [
    { score: '50%', coins: 0, color: '#666666', icon: 'trending-down' },
    { score: '60%', coins: 1, color: '#555555', icon: 'trending-up' },
    { score: '70%', coins: 2, color: '#444444', icon: 'trending-up' },
    { score: '80%', coins: 3, color: '#333333', icon: 'trending-up' },
    { score: '90%', coins: 4, color: '#222222', icon: 'star' },
    { score: '100%', coins: 5, color: '#FD904B', icon: 'star' },
  ];

  const streakBonuses = [
    { title: 'Daily Streak', coins: '+1 coin/day', icon: 'local-fire-department' },
    { title: 'Weekly Challenge', coins: '+5 coins', icon: 'emoji-events' },
    { title: 'Perfect Score', coins: '+3 coins', icon: 'stars' },
  ];

  const quizStats = [
    { label: 'Questions', value: '10', icon: 'quiz', color: '#333333' },
    { label: 'Duration', value: '8 min', icon: 'timer', color: '#444444' },
    { label: 'Max Coins', value: '25', icon: 'monetization-on', color: '#FD904B' },
    { label: 'Difficulty', value: 'Medium', icon: 'trending-up', color: '#555555' },
  ];

  const themeColors = {
    background: isDarkMode ? '#000000' : '#FFFFFF',
    card: isDarkMode ? '#1A1A1A' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    muted: isDarkMode ? '#CCCCCC' : '#666666',
    border: isDarkMode ? '#333333' : '#E0E0E0',
    iconBg: isDarkMode ? '#333333' : '#F0F0F0',
    accent: '#FD904B',
    success: '#000000',
    warning: '#666666',
    error: '#333333',
  };

  const renderProfilePhoto = (user: TopPerformer, size = 40, rank?: number) => {
    const initials = `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase();
    let borderColor = themeColors.border;
    let borderWidth = 1;

    if (rank === 1) {
      borderColor = '#FFD700';
      borderWidth = 2;
    } else if (rank === 2) {
      borderColor = '#C0C0C0';
    } else if (rank === 3) {
      borderColor = '#CD7F32';
    }

    if (user.profilePhoto) {
      const imageUrl = user.profilePhoto.includes('http') 
        ? user.profilePhoto 
        : `${imgBaseUrl}/${user.profilePhoto}`;
      
      return (
        <View
          style={{
            width: size,
            height: size,
            borderRadius: size / 2,
            borderWidth,
            borderColor,
            overflow: 'hidden',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Image
            source={{ uri: imageUrl }}
            style={{
              width: size,
              height: size,
            }}
            resizeMode="cover"
            onError={(e) => console.log('Image load error:', e.nativeEvent.error)}
          />
        </View>
      );
    }

    return (
      <View
        style={{
          width: size,
          height: size,
          borderRadius: size / 2,
          borderWidth,
          borderColor,
          backgroundColor: rank === 1 ? '#FFD700' : themeColors.iconBg,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Text
          style={{
            fontSize: size / 2.5,
            fontWeight: '700',
            color: rank === 1 ? '#FFFFFF' : themeColors.text,
          }}>
          {initials}
        </Text>
      </View>
    );
  };

  const renderPerformerItem = ({ item, index }: { item: TopPerformer, index: number }) => {
    const isTopThree = item.rank <= 3;
    let ribbonColor = '';
    
    if (item.rank === 1) ribbonColor = '#FFD700';
    else if (item.rank === 2) ribbonColor = '#C0C0C0';
    else if (item.rank === 3) ribbonColor = '#CD7F32';
    
    return (
      <View style={[styles.performerCard, { 
        backgroundColor: themeColors.card,
        borderColor: themeColors.border,
        marginLeft: index === 0 ? 20 : 0,
      }]}>
        {isTopThree && (
          <View style={[styles.rankRibbon, { backgroundColor: ribbonColor }]}>
            <MaterialIcons 
              name="emoji-events" 
              size={16} 
              color="#FFFFFF" 
              style={{ marginRight: 4 }}
            />
            <Text style={styles.rankRibbonText}>{item.rank}</Text>
          </View>
        )}

        <View style={styles.performerContent}>
          <View style={styles.performerHeader}>
            {renderProfilePhoto(item, 48, item.rank)}
            <View style={styles.performerInfo}>
              <Text style={[styles.performerName, { color: themeColors.text }]}>
                {item.firstName} {item.lastName}
              </Text>
              <Text style={[styles.performerRank, { 
                color: isTopThree ? themeColors.text : themeColors.muted 
              }]}>
                Rank #{item.rank}
              </Text>
            </View>
          </View>

          <View style={styles.performerStats}>
            <View style={[styles.statBadge, { backgroundColor: themeColors.iconBg }]}>
              <MaterialIcons name="quiz" size={14} color={themeColors.text} />
              <Text style={[styles.statText, { color: themeColors.text }]}>
                Score: {item.score}
              </Text>
            </View>
            
            <View style={[styles.statBadge, { backgroundColor: '#FFF5E6' }]}>
              <MaterialIcons name="local-fire-department" size={14} color="#FF9500" />
              <Text style={[styles.statText, { color: '#FF9500' }]}>
                Streak: {item.streakCount}d
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaProvider>
      <NavigationHeader title="Daily Quiz" onBackPress={() => {}} />
      <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]} edges={['left', 'right']}>
        <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
          {/* Yesterday's Top Performers Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="emoji-events" size={24} color={themeColors.text} />
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Yesterday's Top Performers
              </Text>
            </View>

            {loadingPerformers ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={themeColors.accent} />
              </View>
            ) : errorMessage ? (
              <Text style={[styles.errorText, { color: themeColors.error }]}>
                {errorMessage}. Showing fallback data.
              </Text>
            ) : topPerformers.length === 0 ? (
              <View style={[styles.emptyState, { backgroundColor: themeColors.card }]}>
                <MaterialIcons name="sentiment-dissatisfied" size={40} color={themeColors.muted} />
                <Text style={[styles.emptyText, { color: themeColors.muted }]}>
                  No top performers available
                </Text>
              </View>
            ) : (
              <FlatList
                data={topPerformers}
                renderItem={renderPerformerItem}
                keyExtractor={(item) => `${item.rank}`}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.performersList}
                snapToInterval={width * 0.8 + 20}
                decelerationRate="fast"
              />
            )}
          </View>

          {/* Hero Section */}
          {/* <ShadowStyle style={[styles.heroSection, { backgroundColor: themeColors.card }]}>
            <View style={styles.heroContent}>
              <View style={styles.heroIcon}>
                <View style={[styles.iconGradient, { backgroundColor: themeColors.accent }]}>
                  <MaterialIcons name="quiz" size={32} color="#FFFFFF" />
                </View>
              </View>
              <Text style={[styles.heroTitle, { color: themeColors.text }]}>Daily Quiz</Text>
              <Text style={[styles.heroSubtitle, { color: themeColors.muted }]}>
                Stay informed, test your knowledge, and earn exclusive rewards!
              </Text>
            </View>
          </ShadowStyle> */}


          <ShadowStyle style={[styles.heroSection, { backgroundColor: themeColors.card }]}>
  <View style={styles.heroContent}>
    <View style={styles.heroIcon}>
      <View style={[styles.iconGradient, { backgroundColor: themeColors.accent }]}>
        <MaterialIcons name="quiz" size={32} color="#FFFFFF" />
      </View>
    </View>
    <Text style={[styles.heroTitle, { color: themeColors.text }]}>Daily Quiz</Text>
    <Text style={[styles.heroSubtitle, { color: themeColors.muted }]}>
      Stay informed, test your knowledge, and earn exclusive rewards!
    </Text>
  </View>
</ShadowStyle>

          <View style={[styles.statsGrid]}>
            {quizStats.map((stat, index) => (
              <ShadowStyle
                key={index}
                style={[styles.statCard, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}>
                <View style={[styles.statIcon, { backgroundColor: stat.color + '20' }]}>
                  <MaterialIcons name={stat.icon as any} size={20} color={stat.color} />
                </View>
                <Text style={[styles.statValue, { color: themeColors.text }]}>{stat.value}</Text>
                <Text style={[styles.statLabel, { color: themeColors.muted }]}>{stat.label}</Text>
              </ShadowStyle>
            ))}
          </View>

          {/* Main CTA Button */}
          <TouchableOpacity
            style={[styles.primaryBtn, { backgroundColor: themeColors.accent }]}
            onPress={async () => {
              try {
                const token = await AsyncStorage.getItem('token');
                if (token) {
                  Linking.openURL(`${WebUrl}/mock-test/?token=${token}`);
                } else {
                  Toast.show('Please log in to start the quiz', Toast.SHORT);
                }
              } catch (error) {
                console.error('Error opening quiz URL:', error);
                Toast.show('Failed to start quiz', Toast.SHORT);
              }
            }}>
            <MaterialIcons name="play-arrow" size={24} color="#FFFFFF" />
            <Text style={styles.primaryBtnText}>Start Daily Quiz</Text>
            <MaterialIcons name="arrow-forward" size={20} color="#FFFFFF" />
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionBtn, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}
              onPress={() => navigation.navigate('DailyQuizLeaderboard')}>
              <View style={[styles.actionBtnIcon, { backgroundColor: themeColors.iconBg }]}>
                <MaterialIcons name="leaderboard" size={20} color={themeColors.text} />
              </View>
              <Text style={[styles.actionBtnText, { color: themeColors.text }]}>Leaderboard</Text>
              <MaterialIcons name="chevron-right" size={20} color={themeColors.muted} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionBtn, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}
              onPress={() => navigation.navigate('DailyResults')}>
              <View style={[styles.actionBtnIcon, { backgroundColor: themeColors.iconBg }]}>
                <MaterialIcons name="analytics" size={20} color={themeColors.text} />
              </View>
              <Text style={[styles.actionBtnText, { color: themeColors.text }]}>My Results</Text>
              <MaterialIcons name="chevron-right" size={20} color={themeColors.muted} />
            </TouchableOpacity>
          </View>

          {/* Rewards Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="stars" size={24} color={themeColors.text} />
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Coin Rewards
              </Text>
            </View>

            <View style={styles.rewardsGrid}>
              {rewardTiers.map((item, index) => (
                <View
                  key={index}
                  style={[styles.rewardCard, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}>
                  <View style={[styles.rewardIcon, { backgroundColor: themeColors.iconBg }]}>
                    <MaterialIcons name={item.icon as any} size={18} color={item.color} />
                  </View>
                  <Text style={[styles.rewardScore, { color: themeColors.text }]}>{item.score}</Text>
                  <View style={[styles.rewardPill, { backgroundColor: item.color }]}>
                    <MaterialIcons name="monetization-on" size={12} color="#FFFFFF" />
                    <Text style={styles.pillText}>{item.coins}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Streak Bonuses Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="local-fire-department" size={24} color={themeColors.text} />
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Streak Bonuses
              </Text>
            </View>

            <View style={styles.streakContainer}>
              {streakBonuses.map((item, index) => (
                <View
                  key={index}
                  style={[styles.streakCard, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}>
                  <View style={[styles.streakIcon, { backgroundColor: themeColors.iconBg }]}>
                    <MaterialIcons name={item.icon as any} size={20} color={themeColors.text} />
                  </View>
                  <View style={styles.streakContent}>
                    <Text style={[styles.streakTitle, { color: themeColors.text }]}>{item.title}</Text>
                    <Text style={[styles.streakReward, { color: themeColors.muted }]}>{item.coins}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Badges Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="emoji-events" size={24} color={themeColors.text} />
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Achievement Badges
              </Text>
            </View>

            <View style={styles.badgeContainer}>
              {[
                { image: IMAGE_CONSTANT.BADGE100, title: '100 Coins', bgColor: '#333333' },
                { image: IMAGE_CONSTANT.BADGE1000, title: '500 Coins', bgColor: '#444444' },
                { image: IMAGE_CONSTANT.BADGE10000, title: '1000 Coins', bgColor: '#FD904B' },
                { image: IMAGE_CONSTANT.BADGEMONTH, title: '30 Days', bgColor: '#555555' },
                { image: IMAGE_CONSTANT.BADGEYEAR, title: '365 Days', bgColor: '#666666' },
                { image: IMAGE_CONSTANT.BADGESTREAK, title: 'Daily Streak', bgColor: '#777777' },
              ].map((badge, index) => (
                <View
                  key={index}
                  style={[styles.badgeItem, { backgroundColor: themeColors.card, borderColor: themeColors.border }]}>
                  <View
                    style={[styles.badgeImageContainer, { backgroundColor: badge.bgColor === '#FD904B' ? badge.bgColor : themeColors.iconBg }]}>
                    <Image source={badge.image} style={styles.badgeImage} />
                  </View>
                  <Text style={[styles.badgeLabel, { color: themeColors.text }]}>{badge.title}</Text>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  sectionContainer: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  performersList: {
    paddingRight: 20,
  },
  performerCard: {
    width: width * 0.8,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 16,
    padding: 16,
    position: 'relative',
    overflow: 'hidden',
  },
  rankRibbon: {
    position: 'absolute',
    top: 16,
    right: -24,
    width: 96,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    transform: [{ rotate: '45deg' }],
  },
  rankRibbonText: {
    color: '#FFFFFF',
    fontWeight: '800',
    fontSize: 12,
  },
  performerContent: {
    flex: 1,
  },
  performerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  performerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  performerName: {
    fontSize: 16,
    fontWeight: '700',
  },
  performerRank: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 2,
  },
  performerStats: {
    flexDirection: 'row',
    gap: 8,
  },
  statBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    gap: 4,
  },
  statText: {
    fontSize: 12,
    fontWeight: '600',
  },
  loadingContainer: {
    height: 180,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 20,
    paddingHorizontal: 20,
  },
  emptyState: {
    height: 180,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    marginHorizontal: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
    fontWeight: '500',
  },
  heroSection: {
    paddingHorizontal: 20,
    paddingVertical: 30,
    marginBottom: 20,
    borderWidth: 0,
  },
  heroContent: {
    alignItems: 'center',
  },
  heroIcon: {
    marginBottom: 16,
  },
  iconGradient: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  heroSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: (width - 56) / 2,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  primaryBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 24,
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
    gap: 12,
  },
  primaryBtnText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  actionButtons: {
    paddingHorizontal: 20,
    marginBottom: 32,
    gap: 12,
  },
  actionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
  },
  actionBtnIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  actionBtnText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
  },
  rewardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  rewardCard: {
    flex: 1,
    minWidth: (width - 64) / 3,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  rewardIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  rewardScore: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 6,
  },
  rewardPill: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  pillText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  streakContainer: {
    gap: 12,
  },
  streakCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  streakIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  streakContent: {
    flex: 1,
  },
  streakTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  streakReward: {
    fontSize: 14,
    fontWeight: '500',
  },
  badgeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  badgeItem: {
    flex: 1,
    minWidth: (width - 64) / 3,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  badgeImageContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  badgeImage: {
    width: 32,
    height: 32,
    resizeMode: 'contain',
  },
  badgeLabel: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
