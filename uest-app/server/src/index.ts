import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import helmet from "helmet";
import rateLimit, { RateLimitRequestHandler } from 'express-rate-limit';
import path from "path";
import router from "./router";
import cookieParser from "cookie-parser";
import http from 'http';
import { io } from './socket/socket';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 4005;
const server = http.createServer(app);
app.use(
  helmet({
    crossOriginResourcePolicy: {
      policy: "same-site",
    },
  })
);
io.attach(server);

const allowedOrigins = [process.env.FRONTEND_URL, process.env.ADMIN_URL, "*", process.env.DASHBOARD_URL, "192.168.29.133"];

app.use(
  cors({
    origin: (origin, callback) => {
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error("Not allowed by CORS"));
      }
    },
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    credentials: true,
  })
);

app.use(cookieParser());
app.use(express.json({ limit: '5mb' }));
app.use(express.urlencoded({ limit: '5mb', extended: true }));

app.set('trust proxy', true);

const limiter: RateLimitRequestHandler = rateLimit({
  windowMs: 1 * 60 * 1000,
  limit: 300,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req): string => {
    return req.ip || 'unknown';
  },
});

app.use(limiter);

app.disable("x-powered-by");

app.use("/uploads", express.static(path.join(__dirname, "..", "uploads")));

app.use("/api/v1", router);

app.use((err: any, res: express.Response) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

server.listen(4005, "0.0.0.0",() => {
  console.log(`🚀 Server is running on port ${PORT}`);
});

export = app;